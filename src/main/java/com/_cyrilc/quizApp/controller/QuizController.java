package com._cyrilc.quizApp.controller;

import com._cyrilc.quizApp.dto.ScoreResponse;
import com._cyrilc.quizApp.entity.Question;
import com._cyrilc.quizApp.entity.ScoreHistory;
import com._cyrilc.quizApp.entity.User;
import com._cyrilc.quizApp.repository.QuestionRepository;
import com._cyrilc.quizApp.repository.ScoreHistoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/quiz")
public class QuizController {

    @Autowired
    private QuestionRepository questionRepository;
    @Autowired private ScoreHistoryRepository scoreHistoryRepository;

    @GetMapping("/questions")
    public List<Question> getQuestions() {
        List<Question> questions = questionRepository.findAll();
        Collections.shuffle(questions);
        return questions.subList(0, Math.min(10, questions.size()));
    }

    @PostMapping("/submit")
    public ScoreResponse submit(@RequestBody Map<Long, String> answers, @AuthenticationPrincipal User user) {
        int score = 0;
        for (Map.Entry<Long, String> entry : answers.entrySet()) {
            Question question = questionRepository.findById(entry.getKey()).orElse(null);
            if (question != null && question.getCorrectAnswer().equals(entry.getValue())) {
                score++;
            }
        }

        ScoreHistory history = new ScoreHistory();
        history.setUser(user);
        history.setScore(score);
        history.setTimestamp(LocalDateTime.now());
        scoreHistoryRepository.save(history);

        List<ScoreHistory> pastScores = scoreHistoryRepository.findByUser(user);
        return new ScoreResponse(score, pastScores);
    }
}
