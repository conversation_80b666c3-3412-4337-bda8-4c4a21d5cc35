package com._cyrilc.quizApp;

import com._cyrilc.quizApp.entity.Question;
import com._cyrilc.quizApp.repository.QuestionRepository;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
public class DataLoader {

    @Bean
    CommandLineRunner loadData(QuestionRepository questionRepository) {
        return args -> {
            questionRepository.save(new Question("What is the capital of France?", Arrays.asList("Paris", "London", "Berlin", "Madrid"), "Paris"));
            questionRepository.save(new Question("What is 2 + 2?", Arrays.asList("2", "4", "6", "8"), "4"));
            // Add more questions (50-60 total)
        };
    }
}